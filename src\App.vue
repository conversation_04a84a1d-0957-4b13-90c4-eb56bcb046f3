<template>
  <div id="app">
    <h1>人脸识别应用</h1>
    
    <!-- 文件上传区域 -->
    <div class="upload-section">
      <input 
        type="file" 
        @change="handleFileUpload" 
        accept="image/*" 
        ref="fileInput"
        style="display: none"
      />
      <button @click="$refs.fileInput.click()" class="upload-btn">
        选择图片
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      正在加载模型和处理图片...
    </div>

    <!-- 图片显示和人脸检测结果 -->
    <div v-if="imageUrl" class="image-container">
      <canvas 
        ref="canvas" 
        :width="canvasWidth" 
        :height="canvasHeight"
        class="detection-canvas"
      ></canvas>
    </div>

    <!-- 检测结果对比 -->
    <div v-if="faceApiResults.length > 0 || mediaPipeResults.length > 0" class="results-comparison">
      <div class="detection-results">
        <div class="result-section">
          <h3>🔍 Face-API.js 检测结果</h3>
          <div v-if="faceApiResults.length > 0" class="results">
            <p class="result-summary">检测到 {{ faceApiResults.length }} 张人脸</p>
            <div v-for="(result, index) in faceApiResults" :key="'faceapi-' + index" class="face-info faceapi">
              <p>人脸 {{ index + 1 }}: 置信度 {{ (result.detection.score * 100).toFixed(1) }}%</p>
              <p>位置: ({{ result.detection.box.x.toFixed(1) }}, {{ result.detection.box.y.toFixed(1) }})</p>
              <p>尺寸: {{ result.detection.box.width.toFixed(1) }} x {{ result.detection.box.height.toFixed(1) }}</p>
            </div>
          </div>
          <div v-else class="no-results">
            <p>❌ 未检测到人脸</p>
          </div>
        </div>

        <div class="result-section">
          <h3>🎯 MediaPipe 检测结果</h3>
          <div v-if="mediaPipeResults.length > 0" class="results">
            <p class="result-summary">检测到 {{ mediaPipeResults.length }} 张人脸</p>
            <div v-for="(result, index) in mediaPipeResults" :key="'mediapipe-' + index" class="face-info mediapipe">
              <p>人脸 {{ index + 1 }}: 置信度 {{ (result.score[0] * 100).toFixed(1) }}%</p>
              <p>相对位置: ({{ result.boundingBox.originX.toFixed(3) }}, {{ result.boundingBox.originY.toFixed(3) }})</p>
              <p>相对尺寸: {{ result.boundingBox.width.toFixed(3) }} x {{ result.boundingBox.height.toFixed(3) }}</p>
            </div>
          </div>
          <div v-else class="no-results">
            <p>❌ 未检测到人脸</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 控制台日志显示 -->
    <div class="console-logs">
      <h3>🔍 检测日志</h3>
      <div class="log-container" ref="logContainer">
        <div v-for="(log, index) in logs" :key="index" class="log-entry" :class="log.type">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <button @click="clearLogs" class="clear-logs-btn">清空日志</button>
    </div>
  </div>
</template>

<script>
import * as faceapi from 'face-api.js'

export default {
  name: 'App',
  data() {
    return {
      imageUrl: null,
      loading: false,
      modelsLoaded: false,
      mediaPipeLoaded: false,
      faceApiResults: [],
      mediaPipeResults: [],
      canvasWidth: 0,
      canvasHeight: 0,
      logs: []
    }
  },

  computed: {
    canvasWidth() {
      return this.$refs.canvas ? this.$refs.canvas.width : 0
    },
    canvasHeight() {
      return this.$refs.canvas ? this.$refs.canvas.height : 0
    }
  },

  async mounted() {
    await Promise.all([
      this.loadFaceApiModels(),
      this.loadMediaPipeModels()
    ])
  },

  beforeDestroy() {
    // 清理 MediaPipe 实例
    if (this.$mediaPipeFaceDetection) {
      this.$mediaPipeFaceDetection.close()
    }
  },
  methods: {
    // 日志记录方法
    addLog(message, type = 'info') {
      const now = new Date()
      const time = now.toLocaleTimeString()
      this.logs.push({
        time,
        message,
        type
      })

      // 限制日志数量，避免内存占用过多
      if (this.logs.length > 100) {
        this.logs.shift()
      }

      // 自动滚动到最新日志
      this.$nextTick(() => {
        const container = this.$refs.logContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })

      // 同时输出到浏览器控制台
      if (type === 'error') {
        console.error(message)
      } else {
        console.log(message)
      }
    },

    clearLogs() {
      this.logs = []
      this.addLog('📝 日志已清空')
    },
    async loadFaceApiModels() {
      try {
        this.loading = true
        this.addLog('🚀 开始加载人脸识别模型...')

        // 加载人脸检测模型
        this.addLog('📥 正在加载 TinyFaceDetector 模型...')
        await faceapi.nets.tinyFaceDetector.loadFromUri('/models')
        this.addLog('✅ TinyFaceDetector 模型加载完成')

        this.addLog('📥 正在加载 FaceLandmark68Net 模型...')
        await faceapi.nets.faceLandmark68Net.loadFromUri('/models')
        this.addLog('✅ FaceLandmark68Net 模型加载完成')

        this.addLog('📥 正在加载 FaceRecognitionNet 模型...')
        await faceapi.nets.faceRecognitionNet.loadFromUri('/models')
        this.addLog('✅ FaceRecognitionNet 模型加载完成')

        this.modelsLoaded = true
        this.addLog('🎉 Face-API.js 模型加载完成！')
      } catch (error) {
        this.addLog(`❌ Face-API.js 模型加载失败: ${error.message}`, 'error')
        alert('Face-API.js 模型加载失败，请检查网络连接')
      } finally {
        this.loading = false
      }
    },

    async loadMediaPipeModels() {
      try {
        this.addLog('🚀 开始加载 MediaPipe 模型...')

        // 动态导入 MediaPipe 模块以避免响应式代理问题
        const { FaceDetection } = await import('@mediapipe/face_detection')

        // 创建一个非响应式的 MediaPipe 实例
        const faceDetection = new FaceDetection({
          locateFile: (file) => {
            return `https://cdn.jsdelivr.net/npm/@mediapipe/face_detection/${file}`
          }
        })

        faceDetection.setOptions({
          model: 'short',
          minDetectionConfidence: 0.3,  // 降低阈值以提高检测敏感度
        })

        // 将实例存储在非响应式属性中
        this.$mediaPipeFaceDetection = faceDetection

        // 初始化 MediaPipe
        faceDetection.onResults(this.onMediaPipeResults.bind(this))
        await faceDetection.initialize()

        this.mediaPipeLoaded = true
        this.addLog('🎉 MediaPipe 模型加载完成！')

        // 测试 MediaPipe 是否正常工作
        this.addLog('🧪 测试 MediaPipe 回调函数...')
        setTimeout(() => {
          this.onMediaPipeResults({ detections: [] })
          this.addLog('✅ MediaPipe 回调测试完成')
        }, 100)

      } catch (error) {
        this.addLog(`❌ MediaPipe 模型加载失败: ${error.message}`, 'error')
        console.error('MediaPipe 加载错误:', error)
        this.mediaPipeLoaded = false
      }
    },

    onMediaPipeResults(results) {
      // 这个方法会在 MediaPipe 检测完成时被调用
      // 使用非响应式存储避免代理问题
      try {
        console.log('MediaPipe onResults 回调被调用:', results)
        this.addLog('📨 MediaPipe 回调被触发')
        this.addLog(`🔍 MediaPipe 结果类型: ${typeof results}`)
        this.addLog(`📊 MediaPipe 结果长度: ${Array.isArray(results) ? results.length : 'N/A'}`)

        // 直接存储结果，不管是什么格式
        this.$mediaPipeDetectionResults = results
      } catch (error) {
        console.error('MediaPipe onResults 回调错误:', error)
        this.addLog(`❌ MediaPipe 回调处理错误: ${error.message}`, 'error')
        this.$mediaPipeDetectionResults = []
      }
    },



    async handleFileUpload(event) {
      const file = event.target.files[0]
      if (!file) {
        this.addLog('❌ 没有选择文件')
        return
      }

      this.addLog('📁 选择的文件信息:')
      this.addLog(`  - 文件名: ${file.name}`)
      this.addLog(`  - 文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB`)
      this.addLog(`  - 文件类型: ${file.type}`)

      if (!this.modelsLoaded) {
        this.addLog('⚠️ Face-API.js 模型还未加载完成', 'warning')
        alert('Face-API.js 模型还未加载完成，请稍候')
        return
      }

      if (!this.mediaPipeLoaded) {
        this.addLog('⚠️ MediaPipe 模型还未加载完成', 'warning')
        alert('MediaPipe 模型还未加载完成，请稍候')
        return
      }

      this.loading = true
      this.faceApiResults = []
      this.mediaPipeResults = []
      this.addLog('🔄 开始处理图片...')

      try {
        // 创建图片URL
        this.imageUrl = URL.createObjectURL(file)
        this.addLog('✅ 图片URL创建成功')

        // 等待图片加载
        await this.$nextTick()

        // 同时进行两种人脸检测
        await Promise.all([
          this.detectFacesWithFaceApi(),
          this.detectFacesWithMediaPipe()
        ])
      } catch (error) {
        this.addLog(`❌ 图片处理失败: ${error.message}`, 'error')
        alert('图片处理失败')
      } finally {
        this.loading = false
        this.addLog('✅ 图片处理完成')
      }
    },

    async detectFacesWithFaceApi() {
      this.addLog('🔍 开始 Face-API.js 人脸检测...')
      const img = new Image()

      img.onload = async () => {
        try {
          this.addLog('📷 图片加载成功:')
          this.addLog(`  - 图片尺寸: ${img.width} x ${img.height}`)

          // 设置canvas尺寸
          this.canvasWidth = img.width
          this.canvasHeight = img.height
          this.addLog(`✅ Canvas尺寸设置为: ${this.canvasWidth} x ${this.canvasHeight}`)

          await this.$nextTick()

          const canvas = this.$refs.canvas
          const ctx = canvas.getContext('2d')

          // 绘制原图
          ctx.drawImage(img, 0, 0)
          this.addLog('✅ 原图绘制完成')

          // 进行人脸检测
          this.addLog('🤖 正在进行 Face-API.js 人脸检测...')
          const startTime = Date.now()

          const detections = await faceapi.detectAllFaces(
            img,
            new faceapi.TinyFaceDetectorOptions()
          ).withFaceLandmarks().withFaceDescriptors()

          const endTime = Date.now()
          const detectionTime = endTime - startTime

          this.addLog(`⏱️ Face-API.js 检测耗时: ${detectionTime}ms`)
          this.addLog(`🎯 Face-API.js 检测结果: 发现 ${detections.length} 张人脸`)

          // 详细打印每个检测到的人脸信息
          detections.forEach((detection, index) => {
            const { x, y, width, height } = detection.detection.box
            const score = detection.detection.score
            this.addLog(`👤 Face-API.js 人脸 ${index + 1}:`)
            this.addLog(`  - 位置: (${x.toFixed(1)}, ${y.toFixed(1)})`)
            this.addLog(`  - 尺寸: ${width.toFixed(1)} x ${height.toFixed(1)}`)
            this.addLog(`  - 置信度: ${(score * 100).toFixed(1)}%`)
            this.addLog(`  - 特征点数量: ${detection.landmarks.positions.length}`)
          })

          this.faceApiResults = detections

          // 不在这里绘制检测框，等待 MediaPipe 完成后统一绘制
          this.addLog('✅ Face-API.js 检测完成，等待 MediaPipe 检测...')

        } catch (error) {
          this.addLog(`❌ Face-API.js 检测失败: ${error.message}`, 'error')
          this.addLog(`错误详情: ${error.stack}`, 'error')
        }
      }

      img.onerror = (error) => {
        this.addLog(`❌ Face-API.js 图片加载失败: ${error}`, 'error')
      }

      img.src = this.imageUrl
      this.addLog('📥 Face-API.js 开始加载图片...')
    },

    async detectFacesWithMediaPipe() {
      this.addLog('🔍 开始 MediaPipe 人脸检测...')

      try {
        if (!this.$mediaPipeFaceDetection) {
          this.addLog('❌ MediaPipe 未初始化', 'error')
          this.mediaPipeResults = []
          return
        }

        const img = new Image()

        await new Promise((resolve) => {
          img.onload = async () => {
            try {
              this.addLog('📷 MediaPipe 图片加载成功')
              this.addLog(`  - 图片尺寸: ${img.width} x ${img.height}`)

              // 创建 canvas 来处理图片
              const canvas = document.createElement('canvas')
              const ctx = canvas.getContext('2d')
              canvas.width = img.width
              canvas.height = img.height
              ctx.drawImage(img, 0, 0)

              this.addLog('🤖 正在进行 MediaPipe 人脸检测...')
              const startTime = Date.now()

              // 重置检测结果
              this.$mediaPipeDetectionResults = null

              // 发送图片到 MediaPipe 进行检测
              this.addLog('🔄 发送图片到 MediaPipe...')
              try {
                await this.$mediaPipeFaceDetection.send({ image: img })
                this.addLog('✅ 图片已发送到 MediaPipe')
              } catch (sendError) {
                this.addLog(`❌ 发送图片到 MediaPipe 失败: ${sendError.message}`, 'error')
                throw sendError
              }

              // 等待检测结果，设置超时
              await new Promise((resolveDetection) => {
                let timeoutId = null
                const checkResults = () => {
                  if (this.$mediaPipeDetectionResults !== null) {
                    if (timeoutId) clearTimeout(timeoutId)

                    // 处理检测结果
                    const endTime = Date.now()
                    const detectionTime = endTime - startTime

                    const results = this.$mediaPipeDetectionResults
                    this.addLog(`⏱️ MediaPipe 检测耗时: ${detectionTime}ms`)

                    // 调试：打印完整的结果结构
                    console.log('MediaPipe 完整结果:', results)
                    this.addLog(`🔍 MediaPipe 结果结构调试: ${JSON.stringify(Object.keys(results))}`)

                    // 检查结果结构并安全处理
                    // MediaPipe 可能直接返回检测数组，而不是 {detections: [...]}
                    const detections = results.detections || results || []
                    this.addLog(`🎯 MediaPipe 检测结果: 发现 ${detections.length} 张人脸`)

                    // 详细打印每个检测到的人脸信息
                    const processedDetections = []
                    detections.forEach((detection, index) => {
                      try {
                        // 打印完整的检测对象结构用于调试
                        console.log(`MediaPipe detection ${index + 1}:`, detection)
                        this.addLog(`🔍 MediaPipe 检测对象 ${index + 1} 的属性: ${JSON.stringify(Object.keys(detection))}`)

                        // MediaPipe 的实际结果格式处理
                        const bbox = detection.boundingBox
                        const score = detection.V && detection.V[0] ? detection.V[0].ga : 0.5

                        this.addLog(`👤 MediaPipe 人脸 ${index + 1}:`)

                        if (bbox) {
                          // MediaPipe 使用 xCenter/yCenter 格式，需要转换为 originX/originY
                          const x = bbox.xCenter - bbox.width / 2
                          const y = bbox.yCenter - bbox.height / 2
                          const w = bbox.width
                          const h = bbox.height

                          this.addLog(`  - 中心坐标: (${bbox.xCenter.toFixed(3)}, ${bbox.yCenter.toFixed(3)})`)
                          this.addLog(`  - 左上角坐标: (${x.toFixed(3)}, ${y.toFixed(3)})`)
                          this.addLog(`  - 相对尺寸: ${w.toFixed(3)} x ${h.toFixed(3)}`)
                          this.addLog(`  - 置信度: ${(score * 100).toFixed(1)}%`)

                          // 标准化检测结果格式
                          processedDetections.push({
                            boundingBox: {
                              originX: x,
                              originY: y,
                              width: w,
                              height: h
                            },
                            score: [score]
                          })
                        } else {
                          this.addLog(`  - ⚠️ 未找到边界框信息`)
                          this.addLog(`  - 置信度: ${(score * 100).toFixed(1)}%`)
                        }

                      } catch (error) {
                        this.addLog(`⚠️ 处理 MediaPipe 检测结果 ${index + 1} 时出错: ${error.message}`, 'warning')
                        console.error('MediaPipe detection processing error:', error)
                        console.log('MediaPipe detection object:', detection)
                      }
                    })

                    this.mediaPipeResults = processedDetections

                    // 两个检测都完成了，现在统一绘制所有检测框
                    this.addLog('🎨 开始绘制所有检测框...')
                    this.drawAllDetections()
                    this.addLog('✅ 所有检测框绘制完成')

                    resolveDetection()
                  } else {
                    setTimeout(checkResults, 50)
                  }
                }

                // 设置3秒超时（缩短超时时间）
                timeoutId = setTimeout(() => {
                  this.addLog('⚠️ MediaPipe 检测超时，使用空结果', 'warning')
                  this.$mediaPipeDetectionResults = []  // 直接设置为空数组
                  this.mediaPipeResults = []

                  // 即使 MediaPipe 超时，也要绘制 Face-API.js 的结果
                  this.addLog('🎨 MediaPipe 超时，绘制 Face-API.js 检测框...')
                  this.drawAllDetections()
                  this.addLog('✅ Face-API.js 检测框绘制完成')

                  resolveDetection()
                }, 3000)

                checkResults()
              })

              resolve()

            } catch (error) {
              this.addLog(`❌ MediaPipe 检测失败: ${error.message}`, 'error')
              this.mediaPipeResults = []
              resolve()
            }
          }

          img.onerror = (error) => {
            this.addLog(`❌ MediaPipe 图片加载失败: ${error}`, 'error')
            this.mediaPipeResults = []
            resolve()
          }

          img.src = this.imageUrl
          this.addLog('📥 MediaPipe 开始加载图片...')
        })

      } catch (error) {
        this.addLog(`❌ MediaPipe 检测过程失败: ${error.message}`, 'error')
        this.mediaPipeResults = []
      }
    },

    drawAllDetections() {
      const canvas = this.$refs.canvas
      const ctx = canvas.getContext('2d')

      // 先绘制 Face-API.js 检测框
      if (this.faceApiResults.length > 0) {
        this.addLog(`🎨 绘制 ${this.faceApiResults.length} 个 Face-API.js 检测框...`)
        this.faceApiResults.forEach((detection, index) => {
          const { x, y, width, height } = detection.detection.box
          const score = detection.detection.score

          this.addLog(`🖼️ 绘制 Face-API.js 人脸 ${index + 1} 的检测框:`)
          this.addLog(`  - 框位置: (${x.toFixed(1)}, ${y.toFixed(1)}, ${width.toFixed(1)}, ${height.toFixed(1)})`)

          // 绘制人脸框 - 绿色
          ctx.strokeStyle = '#00ff00'
          ctx.lineWidth = 3
          ctx.strokeRect(x, y, width, height)

          // 绘制置信度
          ctx.fillStyle = '#00ff00'
          ctx.font = '16px Arial'
          const confidenceText = `Face-API: ${(score * 100).toFixed(1)}%`
          ctx.fillText(confidenceText, x, y - 5)

          this.addLog(`  - 置信度文本: ${confidenceText}`)
        })
      }

      // 再绘制 MediaPipe 检测框
      if (this.mediaPipeResults.length > 0) {
        this.addLog(`🎨 绘制 ${this.mediaPipeResults.length} 个 MediaPipe 检测框...`)
        this.mediaPipeResults.forEach((detection, index) => {
          const bbox = detection.boundingBox
          const score = detection.score[0]

          // 转换相对坐标到像素坐标
          const x = bbox.originX * canvas.width
          const y = bbox.originY * canvas.height
          const width = bbox.width * canvas.width
          const height = bbox.height * canvas.height

          this.addLog(`🖼️ 绘制 MediaPipe 人脸 ${index + 1} 的检测框:`)
          this.addLog(`  - 框位置: (${x.toFixed(1)}, ${y.toFixed(1)}, ${width.toFixed(1)}, ${height.toFixed(1)})`)

          // 绘制人脸框 - 蓝色
          ctx.strokeStyle = '#0066ff'
          ctx.lineWidth = 3
          ctx.strokeRect(x, y, width, height)

          // 绘制置信度
          ctx.fillStyle = '#0066ff'
          ctx.font = '16px Arial'
          const confidenceText = `MediaPipe: ${(score * 100).toFixed(1)}%`
          ctx.fillText(confidenceText, x, y + height + 20)

          this.addLog(`  - 置信度文本: ${confidenceText}`)
        })
      }
    },

    drawFaceApiDetections(ctx, detections) {
      this.addLog(`🎨 开始绘制 ${detections.length} 个 Face-API.js 检测框...`)

      detections.forEach((detection, index) => {
        const { x, y, width, height } = detection.detection.box
        const score = detection.detection.score

        this.addLog(`🖼️ 绘制 Face-API.js 人脸 ${index + 1} 的检测框:`)
        this.addLog(`  - 框位置: (${x.toFixed(1)}, ${y.toFixed(1)}, ${width.toFixed(1)}, ${height.toFixed(1)})`)

        // 绘制人脸框 - 绿色
        ctx.strokeStyle = '#00ff00'
        ctx.lineWidth = 3
        ctx.strokeRect(x, y, width, height)

        // 绘制置信度
        ctx.fillStyle = '#00ff00'
        ctx.font = '16px Arial'
        const confidenceText = `Face-API: ${(score * 100).toFixed(1)}%`
        ctx.fillText(confidenceText, x, y - 5)

        this.addLog(`  - 置信度文本: ${confidenceText}`)
      })

      this.addLog('✅ Face-API.js 检测框绘制完成')
    },

    drawMediaPipeDetections() {
      if (this.mediaPipeResults.length === 0) return

      const canvas = this.$refs.canvas
      const ctx = canvas.getContext('2d')

      this.addLog(`🎨 开始绘制 ${this.mediaPipeResults.length} 个 MediaPipe 检测框...`)

      this.mediaPipeResults.forEach((detection, index) => {
        const bbox = detection.boundingBox
        const score = detection.score[0]

        // 转换相对坐标到像素坐标
        const x = bbox.originX * canvas.width
        const y = bbox.originY * canvas.height
        const width = bbox.width * canvas.width
        const height = bbox.height * canvas.height

        this.addLog(`🖼️ 绘制 MediaPipe 人脸 ${index + 1} 的检测框:`)
        this.addLog(`  - 框位置: (${x.toFixed(1)}, ${y.toFixed(1)}, ${width.toFixed(1)}, ${height.toFixed(1)})`)

        // 绘制人脸框 - 蓝色
        ctx.strokeStyle = '#0066ff'
        ctx.lineWidth = 3
        ctx.strokeRect(x, y, width, height)

        // 绘制置信度
        ctx.fillStyle = '#0066ff'
        ctx.font = '16px Arial'
        const confidenceText = `MediaPipe: ${(score * 100).toFixed(1)}%`
        ctx.fillText(confidenceText, x, y + height + 20)

        this.addLog(`  - 置信度文本: ${confidenceText}`)
      })

      this.addLog('✅ MediaPipe 检测框绘制完成')
    },


  }
}
</script>

<style scoped>
.upload-section {
  margin: 20px 0;
}

.upload-btn {
  background-color: #42b883;
  color: white;
  border: none;
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.upload-btn:hover {
  background-color: #369870;
}

.loading {
  margin: 20px 0;
  font-size: 18px;
  color: #666;
}

.image-container {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}

.detection-canvas {
  max-width: 100%;
  height: auto;
  border: 2px solid #ddd;
  border-radius: 8px;
}

/* 对比结果样式 */
.results-comparison {
  margin: 20px 0;
}

.detection-results {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 20px 0;
}

.result-section {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.result-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
}

.result-summary {
  font-weight: bold;
  margin-bottom: 15px;
  font-size: 16px;
}

.results {
  margin: 0;
}

.face-info {
  margin: 10px 0;
  padding: 10px;
  background-color: white;
  border-radius: 4px;
  font-size: 14px;
}

.face-info.faceapi {
  border-left: 4px solid #00ff00;
}

.face-info.mediapipe {
  border-left: 4px solid #0066ff;
}

.no-results {
  padding: 20px;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detection-results {
    grid-template-columns: 1fr;
  }
}

/* 日志显示样式 */
.console-logs {
  margin: 20px 0;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.console-logs h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #495057;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-entry {
  margin-bottom: 2px;
  padding: 2px 0;
  word-wrap: break-word;
}

.log-entry.error {
  color: #dc3545;
}

.log-entry.warning {
  color: #fd7e14;
}

.log-entry.info {
  color: #495057;
}

.log-time {
  color: #6c757d;
  margin-right: 8px;
  font-size: 11px;
}

.log-message {
  color: inherit;
}

.clear-logs-btn {
  margin-top: 10px;
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  font-size: 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.clear-logs-btn:hover {
  background-color: #5a6268;
}

@media (prefers-color-scheme: dark) {
  .result-section {
    background-color: #2d3748;
    border-color: #4a5568;
  }

  .result-section h3 {
    color: #e2e8f0;
  }

  .result-summary {
    color: #e2e8f0;
  }

  .face-info {
    background-color: #1a202c;
    color: #e2e8f0;
  }

  .no-results {
    color: #a0aec0;
  }

  .console-logs {
    background-color: #2d3748;
    border-color: #4a5568;
  }

  .console-logs h3 {
    color: #e2e8f0;
  }

  .log-container {
    background-color: #1a202c;
    border-color: #4a5568;
    color: #e2e8f0;
  }

  .log-entry.info {
    color: #e2e8f0;
  }

  .log-time {
    color: #a0aec0;
  }
}
</style>
