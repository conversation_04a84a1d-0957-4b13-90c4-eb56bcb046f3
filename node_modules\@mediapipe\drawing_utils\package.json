{"name": "@mediapipe/drawing_utils", "version": "0.3.**********", "description": "Mediapipe Simple Drawing Utilities", "main": "drawing_utils.js", "module": "drawing_utils.js", "jsdelivr": "drawing_utils.js", "unpkg": "drawing_utils.js", "types": "index.d.ts", "author": "<EMAIL>", "license": "Apache-2.0", "homepage": "https://google.github.io/mediapipe/solutions", "keywords": ["AR", "ML", "Augmented"], "devDependencies": {}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {}, "browser": {"node-fetch": false, "util": false, "crypto": false}, "sideEffects": []}