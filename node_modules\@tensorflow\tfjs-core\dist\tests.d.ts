/**
 * @license
 * Copyright 2019 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import './browser_util_test';
import './buffer_test';
import './debug_mode_test';
import './engine_test';
import './environment_test';
import './flags_test';
import './globals_test';
import './gradients_test';
import './io/browser_files_test';
import './io/http_test';
import './io/indexed_db_test';
import './io/io_utils_test';
import './io/local_storage_test';
import './io/model_management_test';
import './io/passthrough_test';
import './io/progress_test';
import './io/router_registry_test';
import './io/weights_loader_test';
import './jasmine_util_test';
import './kernel_registry_test';
import './ops/arithmetic_test';
import './ops/array_ops_test';
import './ops/axis_util_test';
import './ops/batchnorm_test';
import './ops/binary_ops_test';
import './ops/boolean_mask_test';
import './ops/broadcast_util_test';
import './ops/clone_test';
import './ops/compare_ops_test';
import './ops/complex_ops_test';
import './ops/concat_test';
import './ops/concat_util_test';
import './ops/confusion_matrix_test';
import './ops/conv1d_test';
import './ops/conv2d_depthwise_test';
import './ops/conv2d_separable_test';
import './ops/conv2d_test';
import './ops/conv2d_transpose_test';
import './ops/conv3d_test';
import './ops/conv3d_transpose_test';
import './ops/conv_util_test';
import './ops/diag_test';
import './ops/dropout_test';
import './ops/dropout_util_test';
import './ops/fused_test';
import './ops/gather_nd_test';
import './ops/image_ops_test';
import './ops/in_top_k_test';
import './ops/linalg_ops_test';
import './ops/logical_ops_test';
import './ops/loss_ops_test';
import './ops/lrn_test';
import './ops/lstm_test';
import './ops/matmul_test';
import './ops/moving_average_test';
import './ops/multinomial_test';
import './ops/operation_test';
import './ops/pad_test';
import './ops/pool_test';
import './ops/rand_test';
import './ops/reduction_ops_test';
import './ops/resize_bilinear_test';
import './ops/resize_nearest_neighbor_test';
import './ops/reverse_test';
import './ops/scatter_nd_test';
import './ops/segment_ops_test';
import './ops/signal_ops_test';
import './ops/slice_test';
import './ops/slice_util_test';
import './ops/softmax_test';
import './ops/sparse_to_dense_test';
import './ops/spectral_ops_test';
import './ops/strided_slice_test';
import './ops/topk_test';
import './ops/transpose_test';
import './ops/unary_ops_test';
import './optimizers/adadelta_optimizer_test';
import './optimizers/adagrad_optimizer_test';
import './optimizers/adam_optimizer_test';
import './optimizers/adamax_optimizer_test';
import './optimizers/momentum_optimizer_test';
import './optimizers/optimizer_test';
import './optimizers/rmsprop_optimizer_test';
import './optimizers/sgd_optimizer_test';
import './platforms/platform_browser_test';
import './platforms/platform_node_test';
import './profiler_test';
import './serialization_test';
import './tape_test';
import './tensor_test';
import './tensor_util_test';
import './test_util_test';
import './types_test';
import './util_test';
import './variable_test';
import './version_test';
import './worker_node_test';
import './worker_test';
