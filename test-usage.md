# 测试使用说明

## 如何测试人脸识别功能

1. **启动应用**
   - 确保开发服务器正在运行 (`npm run dev`)
   - 在浏览器中打开 `http://localhost:3000`

2. **等待模型加载**
   - 页面加载后，应用会自动开始加载人脸识别模型
   - 看到"正在加载模型和处理图片..."提示时请耐心等待
   - 模型加载完成后，"选择图片"按钮会变为可用状态

3. **上传测试图片**
   - 点击"选择图片"按钮
   - 选择一张包含人脸的图片（支持 JPG、PNG、WebP 格式）
   - 建议使用清晰、光线充足的人脸照片

4. **查看检测结果**
   - 上传后，应用会自动进行人脸检测
   - 检测到的人脸会用绿色边界框标出
   - 每个人脸框上方会显示检测置信度百分比
   - 页面下方会显示检测到的人脸总数和详细信息

## 测试建议

### 推荐的测试图片类型：
- ✅ 正面人脸照片
- ✅ 多人合照
- ✅ 清晰的证件照
- ✅ 光线充足的自拍照

### 可能检测效果不佳的图片：
- ❌ 侧脸或背面
- ❌ 光线过暗的照片
- ❌ 人脸过小或模糊
- ❌ 被遮挡的人脸

## 功能验证清单

- [ ] 页面正常加载
- [ ] 模型文件成功加载（无错误提示）
- [ ] 文件上传功能正常
- [ ] 人脸检测功能正常
- [ ] 绿色边界框正确显示
- [ ] 置信度百分比显示
- [ ] 检测结果统计显示
- [ ] 响应式布局在不同屏幕尺寸下正常

## 常见问题

### 1. 模型加载失败
- 检查网络连接
- 确认 `public/models` 目录下有所有必需的模型文件
- 重新运行 `npm run download-models`

### 2. 图片上传后无反应
- 确认选择的是支持的图片格式
- 检查浏览器控制台是否有错误信息
- 尝试使用更小尺寸的图片

### 3. 检测不到人脸
- 尝试使用更清晰的人脸图片
- 确保人脸在图片中占有足够大的比例
- 检查图片中的人脸是否正面且光线充足

## 性能说明

- 首次加载需要下载约 6MB 的模型文件
- 图片处理时间取决于图片大小和复杂度
- 建议使用现代浏览器以获得最佳性能
