/**
 * @license
 * Copyright 2019 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as gpgpu_util from './backends/webgl/gpgpu_util';
import * as webgl_util from './backends/webgl/webgl_util';
export { MathBackendWebGL, WebGLMemoryInfo, WebGLTimingInfo } from './backends/webgl/backend_webgl';
export { setWebGLContext } from './backends/webgl/canvas_util';
export { GPGPUContext } from './backends/webgl/gpgpu_context';
export { GPGPUProgram } from './backends/webgl/gpgpu_math';
export { gpgpu_util, webgl_util };
/**
 * Enforce use of half precision textures if available on the platform.
 */
/** @doc {heading: 'Environment', namespace: 'webgl'} */
export declare function forceHalfFloat(): void;
