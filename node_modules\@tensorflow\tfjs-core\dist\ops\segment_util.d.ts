/**
 * @license
 * Copyright 2018 Google Inc. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { Tensor } from '../tensor';
export interface SegOpInfo {
    windowSize: number;
    batchSize: number;
    inSize: number;
    numSegments: number;
}
export declare function segOpComputeOptimalWindowSize(inSize: number, numSegments: number): number;
export declare function computeOutShape(aShape: number[], axis: number, numSegments: number): number[];
export interface GatherOpShapeInfo {
    batchSize: number;
    sliceSize: number;
    dimSize: number;
    outputShape: number[];
}
export declare function collectGatherOpShapeInfo(x: Tensor, indices: Tensor, axis: number): GatherOpShapeInfo;
