{"name": "faceapi-vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "download-models": "node download-models.js", "setup": "npm install && npm run download-models"}, "dependencies": {"@mediapipe/camera_utils": "^0.3.1675466862", "@mediapipe/drawing_utils": "^0.3.1675466124", "@mediapipe/face_detection": "^0.4.1646425229", "face-api.js": "^0.22.2", "vue": "^3.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "vite": "^5.0.0"}}